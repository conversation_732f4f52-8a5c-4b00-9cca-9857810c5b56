{"classifiers": ["Development Status :: 6 - <PERSON><PERSON>", "Intended Audience :: Science/Research", "Operating System :: OS Independent", "License :: OSI Approved :: BSD License", "Topic :: Scientific/Engineering :: Astronomy", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 2.7", "Programming Language :: Python :: 3", "Programming Language :: Python :: 3.4", "Programming Language :: Python :: 3.5", "Programming Language :: Python :: 3.6", "Programming Language :: Python :: 3.7", "Programming Language :: Python :: Implementation :: CPython", "Programming Language :: Python :: Implementation :: PyPy"], "extensions": {"python.details": {"contacts": [{"email": "<EMAIL>", "name": "<PERSON><PERSON><PERSON><PERSON>", "role": "author"}], "document_names": {"description": "DESCRIPTION.rst", "license": "LICENSE.txt"}, "project_urls": {"Home": "https://github.com/phn/jdcal"}}}, "generator": "bdist_wheel (0.30.0)", "license": "BSD", "metadata_version": "2.0", "name": "jdcal", "summary": "<PERSON> dates from proleptic Gregorian and Julian calendars.", "version": "1.4.1"}