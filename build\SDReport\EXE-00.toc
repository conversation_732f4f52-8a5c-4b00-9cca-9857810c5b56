('G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\dist\\SDReport.exe',
 True,
 False,
 False,
 None,
 None,
 False,
 False,
 '<?xml version="1.0" encoding="UTF-8" standalone="yes"?><assembly manifestVersion="1.0" xmlns="urn:schemas-microsoft-com:asm.v1"><assemblyIdentity name="SDReport" processorArchitecture="amd64" type="win32" version="1.0.0.0"/><trustInfo xmlns="urn:schemas-microsoft-com:asm.v3"><security><requestedPrivileges><requestedExecutionLevel level="asInvoker" uiAccess="false"/></requestedPrivileges></security></trustInfo><dependency><dependentAssembly><assemblyIdentity language="*" name="Microsoft.Windows.Common-Controls" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" type="win32" version="6.0.0.0"/><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"/></dependentAssembly></dependency><compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1"><application><supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f0}"/><supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/><supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/><supportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/><supportedOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/></application></compatibility></assembly>',
 True,
 'SDReport.pkg',
 [('PYZ-00.pyz',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\build\\SDReport\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\lib\\struct.pyo',
   'PYMODULE'),
  ('pyimod01_os_path',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod01_os_path.pyc',
   'PYMODULE'),
  ('pyimod02_archive',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod02_archive.pyc',
   'PYMODULE'),
  ('pyimod03_importers',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\loader\\pyimod03_importers.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_win32comgenpy',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_win32comgenpy.py',
   'PYSOURCE'),
  ('pyi_rth_certifi',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\rthooks\\pyi_rth_certifi.py',
   'PYSOURCE'),
  ('SDReport',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\SDReport.py',
   'PYSOURCE'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\Scripts\\VCRUNTIME140.dll',
   'BINARY'),
  ('python37.dll',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\Scripts\\python37.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\TortoiseSVN\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('pythoncom37.dll',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\pywin32_system32\\pythoncom37.dll',
   'BINARY'),
  ('pywintypes37.dll',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\pywin32_system32\\pywintypes37.dll',
   'BINARY'),
  ('select',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\select.pyd',
   'EXTENSION'),
  ('_socket',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_socket.pyd',
   'EXTENSION'),
  ('_ssl',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_ssl.pyd',
   'EXTENSION'),
  ('win32trace',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\win32\\win32trace.pyd',
   'EXTENSION'),
  ('_win32sysloader',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\win32\\_win32sysloader.pyd',
   'EXTENSION'),
  ('_lzma',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_bz2.pyd',
   'EXTENSION'),
  ('win32ui',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\Pythonwin\\win32ui.pyd',
   'EXTENSION'),
  ('_hashlib',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_hashlib.pyd',
   'EXTENSION'),
  ('pyexpat',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\pyexpat.pyd',
   'EXTENSION'),
  ('win32api',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('unicodedata',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\unicodedata.pyd',
   'EXTENSION'),
  ('_elementtree',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_elementtree.pyd',
   'EXTENSION'),
  ('lxml.etree',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\lxml\\etree.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('lxml._elementpath',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\lxml\\_elementpath.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_contextvars',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_contextvars.pyd',
   'EXTENSION'),
  ('_decimal',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_decimal.pyd',
   'EXTENSION'),
  ('PIL._webp',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PIL\\_webp.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imagingtk',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PIL\\_imagingtk.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('PIL._imaging',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PIL\\_imaging.cp37-win_amd64.pyd',
   'EXTENSION'),
  ('_queue',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\_queue.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('libssl-1_1-x64.dll',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\env\\Scripts\\libssl-1_1-x64.dll',
   'BINARY'),
  ('mfc140u.dll',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\Pythonwin\\mfc140u.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Program Files\\TortoiseSVN\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('base_library.zip',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\build\\SDReport\\base_library.zip',
   'DATA'),
  ('certifi\\cacert.pem',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('Include\\pyconfig.h',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python37\\Include\\pyconfig.h',
   'DATA'),
  ('SDReport.exe.manifest',
   'G:\\TOOLWORKS\\AterEX\\SDReport\\SDReport\\build\\SDReport\\SDReport.exe.manifest',
   'BINARY'),
  ('pyi-windows-manifest-filename SDReport.exe.manifest', '', 'OPTION')],
 [],
 False,
 False,
 1753335563,
 [('run.exe',
   'G:\\TOOLWORKS\\AterEX\\U3Report\\U3Report\\env\\lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit\\run.exe',
   'EXECUTABLE')])
