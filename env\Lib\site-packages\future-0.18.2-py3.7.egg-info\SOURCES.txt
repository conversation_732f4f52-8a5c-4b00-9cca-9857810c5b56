.travis.yml
LICENSE.txt
MANIFEST.in
README.rst
TESTING.txt
check_rst.sh
futurize.py
pasteurize.py
pip-delete-this-directory.txt
pytest.ini
setup.cfg
setup.py
docs/Makefile
docs/automatic_conversion.rst
docs/bind_method.rst
docs/bytes_object.rst
docs/changelog.rst
docs/compatible_idioms.rst
docs/conf.py
docs/contents.rst.inc
docs/conversion_limitations.rst
docs/credits.rst
docs/custom_iterators.rst
docs/custom_str_methods.rst
docs/dev_notes.rst
docs/development.rst
docs/dict_object.rst
docs/faq.rst
docs/func_annotations.rst
docs/future-builtins.rst
docs/futureext.py
docs/futurize.rst
docs/futurize_cheatsheet.rst
docs/futurize_overview.rst
docs/hindsight.rst
docs/imports.rst
docs/index.rst
docs/int_object.rst
docs/isinstance.rst
docs/limitations.rst
docs/metaclasses.rst
docs/older_interfaces.rst
docs/open_function.rst
docs/overview.rst
docs/pasteurize.rst
docs/quickstart.rst
docs/reference.rst
docs/roadmap.rst
docs/standard_library_imports.rst
docs/stdlib_incompatibilities.rst
docs/str_object.rst
docs/translation.rst
docs/unicode_literals.rst
docs/upgrading.rst
docs/utilities.rst
docs/what_else.rst
docs/whatsnew.rst
docs/why_python3.rst
docs/3rd-party-py3k-compat-code/astropy_py3compat.py
docs/3rd-party-py3k-compat-code/django_utils_encoding.py
docs/3rd-party-py3k-compat-code/gevent_py3k.py
docs/3rd-party-py3k-compat-code/ipython_py3compat.py
docs/3rd-party-py3k-compat-code/jinja2_compat.py
docs/3rd-party-py3k-compat-code/numpy_py3k.py
docs/3rd-party-py3k-compat-code/pandas_py3k.py
docs/3rd-party-py3k-compat-code/pycrypto_py3compat.py
docs/3rd-party-py3k-compat-code/statsmodels_py3k.py
docs/_static/python-future-icon-32.ico
docs/_static/python-future-icon-white-32.ico
docs/_static/python-future-logo-textless-transparent.png
docs/_static/python-future-logo.png
docs/_static/python-future-logo.tiff
docs/_templates/layout.html
docs/_templates/navbar.html
docs/_templates/sidebarintro.html
docs/_templates/sidebarlogo.html
docs/_templates/sidebartoc.html
docs/_themes/LICENSE
docs/_themes/README
docs/_themes/future/layout.html
docs/_themes/future/relations.html
docs/_themes/future/theme.conf
docs/_themes/future/static/future.css_t
docs/notebooks/Writing Python 2-3 compatible code.ipynb
docs/notebooks/bytes object.ipynb
docs/notebooks/object special methods (next, bool, ...).ipynb
docs/other/auto2to3.py
docs/other/find_pattern.py
docs/other/fix_notebook_html_colour.py
docs/other/lessons.txt
docs/other/todo.txt
docs/other/upload_future_docs.sh
docs/other/useful_links.txt
src/__init__.py
src/_dummy_thread/__init__.py
src/_markupbase/__init__.py
src/_thread/__init__.py
src/builtins/__init__.py
src/copyreg/__init__.py
src/future/__init__.py
src/future.egg-info/PKG-INFO
src/future.egg-info/SOURCES.txt
src/future.egg-info/dependency_links.txt
src/future.egg-info/entry_points.txt
src/future.egg-info/top_level.txt
src/future/backports/__init__.py
src/future/backports/_markupbase.py
src/future/backports/datetime.py
src/future/backports/misc.py
src/future/backports/socket.py
src/future/backports/socketserver.py
src/future/backports/total_ordering.py
src/future/backports/email/__init__.py
src/future/backports/email/_encoded_words.py
src/future/backports/email/_header_value_parser.py
src/future/backports/email/_parseaddr.py
src/future/backports/email/_policybase.py
src/future/backports/email/base64mime.py
src/future/backports/email/charset.py
src/future/backports/email/encoders.py
src/future/backports/email/errors.py
src/future/backports/email/feedparser.py
src/future/backports/email/generator.py
src/future/backports/email/header.py
src/future/backports/email/headerregistry.py
src/future/backports/email/iterators.py
src/future/backports/email/message.py
src/future/backports/email/parser.py
src/future/backports/email/policy.py
src/future/backports/email/quoprimime.py
src/future/backports/email/utils.py
src/future/backports/email/mime/__init__.py
src/future/backports/email/mime/application.py
src/future/backports/email/mime/audio.py
src/future/backports/email/mime/base.py
src/future/backports/email/mime/image.py
src/future/backports/email/mime/message.py
src/future/backports/email/mime/multipart.py
src/future/backports/email/mime/nonmultipart.py
src/future/backports/email/mime/text.py
src/future/backports/html/__init__.py
src/future/backports/html/entities.py
src/future/backports/html/parser.py
src/future/backports/http/__init__.py
src/future/backports/http/client.py
src/future/backports/http/cookiejar.py
src/future/backports/http/cookies.py
src/future/backports/http/server.py
src/future/backports/test/__init__.py
src/future/backports/test/badcert.pem
src/future/backports/test/badkey.pem
src/future/backports/test/dh512.pem
src/future/backports/test/https_svn_python_org_root.pem
src/future/backports/test/keycert.passwd.pem
src/future/backports/test/keycert.pem
src/future/backports/test/keycert2.pem
src/future/backports/test/nokia.pem
src/future/backports/test/nullbytecert.pem
src/future/backports/test/nullcert.pem
src/future/backports/test/pystone.py
src/future/backports/test/sha256.pem
src/future/backports/test/ssl_cert.pem
src/future/backports/test/ssl_key.passwd.pem
src/future/backports/test/ssl_key.pem
src/future/backports/test/ssl_servers.py
src/future/backports/test/support.py
src/future/backports/urllib/__init__.py
src/future/backports/urllib/error.py
src/future/backports/urllib/parse.py
src/future/backports/urllib/request.py
src/future/backports/urllib/response.py
src/future/backports/urllib/robotparser.py
src/future/backports/xmlrpc/__init__.py
src/future/backports/xmlrpc/client.py
src/future/backports/xmlrpc/server.py
src/future/builtins/__init__.py
src/future/builtins/disabled.py
src/future/builtins/iterators.py
src/future/builtins/misc.py
src/future/builtins/new_min_max.py
src/future/builtins/newnext.py
src/future/builtins/newround.py
src/future/builtins/newsuper.py
src/future/moves/__init__.py
src/future/moves/_dummy_thread.py
src/future/moves/_markupbase.py
src/future/moves/_thread.py
src/future/moves/builtins.py
src/future/moves/collections.py
src/future/moves/configparser.py
src/future/moves/copyreg.py
src/future/moves/itertools.py
src/future/moves/pickle.py
src/future/moves/queue.py
src/future/moves/reprlib.py
src/future/moves/socketserver.py
src/future/moves/subprocess.py
src/future/moves/sys.py
src/future/moves/winreg.py
src/future/moves/dbm/__init__.py
src/future/moves/dbm/dumb.py
src/future/moves/dbm/gnu.py
src/future/moves/dbm/ndbm.py
src/future/moves/html/__init__.py
src/future/moves/html/entities.py
src/future/moves/html/parser.py
src/future/moves/http/__init__.py
src/future/moves/http/client.py
src/future/moves/http/cookiejar.py
src/future/moves/http/cookies.py
src/future/moves/http/server.py
src/future/moves/test/__init__.py
src/future/moves/test/support.py
src/future/moves/tkinter/__init__.py
src/future/moves/tkinter/colorchooser.py
src/future/moves/tkinter/commondialog.py
src/future/moves/tkinter/constants.py
src/future/moves/tkinter/dialog.py
src/future/moves/tkinter/dnd.py
src/future/moves/tkinter/filedialog.py
src/future/moves/tkinter/font.py
src/future/moves/tkinter/messagebox.py
src/future/moves/tkinter/scrolledtext.py
src/future/moves/tkinter/simpledialog.py
src/future/moves/tkinter/tix.py
src/future/moves/tkinter/ttk.py
src/future/moves/urllib/__init__.py
src/future/moves/urllib/error.py
src/future/moves/urllib/parse.py
src/future/moves/urllib/request.py
src/future/moves/urllib/response.py
src/future/moves/urllib/robotparser.py
src/future/moves/xmlrpc/__init__.py
src/future/moves/xmlrpc/client.py
src/future/moves/xmlrpc/server.py
src/future/standard_library/__init__.py
src/future/tests/__init__.py
src/future/tests/base.py
src/future/types/__init__.py
src/future/types/newbytes.py
src/future/types/newdict.py
src/future/types/newint.py
src/future/types/newlist.py
src/future/types/newmemoryview.py
src/future/types/newobject.py
src/future/types/newopen.py
src/future/types/newrange.py
src/future/types/newstr.py
src/future/utils/__init__.py
src/future/utils/surrogateescape.py
src/html/__init__.py
src/html/entities.py
src/html/parser.py
src/http/__init__.py
src/http/client.py
src/http/cookiejar.py
src/http/cookies.py
src/http/server.py
src/libfuturize/__init__.py
src/libfuturize/fixer_util.py
src/libfuturize/main.py
src/libfuturize/fixes/__init__.py
src/libfuturize/fixes/fix_UserDict.py
src/libfuturize/fixes/fix_absolute_import.py
src/libfuturize/fixes/fix_add__future__imports_except_unicode_literals.py
src/libfuturize/fixes/fix_basestring.py
src/libfuturize/fixes/fix_bytes.py
src/libfuturize/fixes/fix_cmp.py
src/libfuturize/fixes/fix_division.py
src/libfuturize/fixes/fix_division_safe.py
src/libfuturize/fixes/fix_execfile.py
src/libfuturize/fixes/fix_future_builtins.py
src/libfuturize/fixes/fix_future_standard_library.py
src/libfuturize/fixes/fix_future_standard_library_urllib.py
src/libfuturize/fixes/fix_input.py
src/libfuturize/fixes/fix_metaclass.py
src/libfuturize/fixes/fix_next_call.py
src/libfuturize/fixes/fix_object.py
src/libfuturize/fixes/fix_oldstr_wrap.py
src/libfuturize/fixes/fix_order___future__imports.py
src/libfuturize/fixes/fix_print.py
src/libfuturize/fixes/fix_print_with_import.py
src/libfuturize/fixes/fix_raise.py
src/libfuturize/fixes/fix_remove_old__future__imports.py
src/libfuturize/fixes/fix_unicode_keep_u.py
src/libfuturize/fixes/fix_unicode_literals_import.py
src/libfuturize/fixes/fix_xrange_with_import.py
src/libpasteurize/__init__.py
src/libpasteurize/main.py
src/libpasteurize/fixes/__init__.py
src/libpasteurize/fixes/feature_base.py
src/libpasteurize/fixes/fix_add_all__future__imports.py
src/libpasteurize/fixes/fix_add_all_future_builtins.py
src/libpasteurize/fixes/fix_add_future_standard_library_import.py
src/libpasteurize/fixes/fix_annotations.py
src/libpasteurize/fixes/fix_division.py
src/libpasteurize/fixes/fix_features.py
src/libpasteurize/fixes/fix_fullargspec.py
src/libpasteurize/fixes/fix_future_builtins.py
src/libpasteurize/fixes/fix_getcwd.py
src/libpasteurize/fixes/fix_imports.py
src/libpasteurize/fixes/fix_imports2.py
src/libpasteurize/fixes/fix_kwargs.py
src/libpasteurize/fixes/fix_memoryview.py
src/libpasteurize/fixes/fix_metaclass.py
src/libpasteurize/fixes/fix_newstyle.py
src/libpasteurize/fixes/fix_next.py
src/libpasteurize/fixes/fix_printfunction.py
src/libpasteurize/fixes/fix_raise.py
src/libpasteurize/fixes/fix_raise_.py
src/libpasteurize/fixes/fix_throw.py
src/libpasteurize/fixes/fix_unpacking.py
src/past/__init__.py
src/past/builtins/__init__.py
src/past/builtins/misc.py
src/past/builtins/noniterators.py
src/past/translation/__init__.py
src/past/types/__init__.py
src/past/types/basestring.py
src/past/types/olddict.py
src/past/types/oldstr.py
src/past/utils/__init__.py
src/queue/__init__.py
src/reprlib/__init__.py
src/socketserver/__init__.py
src/tkinter/__init__.py
src/tkinter/colorchooser.py
src/tkinter/commondialog.py
src/tkinter/constants.py
src/tkinter/dialog.py
src/tkinter/dnd.py
src/tkinter/filedialog.py
src/tkinter/font.py
src/tkinter/messagebox.py
src/tkinter/scrolledtext.py
src/tkinter/simpledialog.py
src/tkinter/tix.py
src/tkinter/ttk.py
src/winreg/__init__.py
src/xmlrpc/__init__.py
src/xmlrpc/client.py
src/xmlrpc/server.py
tests/test_future/__init__.py
tests/test_future/test_backports.py
tests/test_future/test_buffer.py
tests/test_future/test_builtins.py
tests/test_future/test_builtins_explicit_import.py
tests/test_future/test_bytes.py
tests/test_future/test_chainmap.py
tests/test_future/test_common_iterators.py
tests/test_future/test_decorators.py
tests/test_future/test_dict.py
tests/test_future/test_email_multipart.py
tests/test_future/test_explicit_imports.py
tests/test_future/test_futurize.py
tests/test_future/test_html.py
tests/test_future/test_htmlparser.py
tests/test_future/test_http_cookiejar.py
tests/test_future/test_httplib.py
tests/test_future/test_import_star.py
tests/test_future/test_imports_httplib.py
tests/test_future/test_imports_urllib.py
tests/test_future/test_int.py
tests/test_future/test_int_old_division.py
tests/test_future/test_isinstance.py
tests/test_future/test_libfuturize_fixers.py
tests/test_future/test_list.py
tests/test_future/test_magicsuper.py
tests/test_future/test_object.py
tests/test_future/test_pasteurize.py
tests/test_future/test_py2_str_literals_to_bytes.py
tests/test_future/test_range.py
tests/test_future/test_requests.py
tests/test_future/test_standard_library.py
tests/test_future/test_str.py
tests/test_future/test_super.py
tests/test_future/test_surrogateescape.py
tests/test_future/test_urllib.py
tests/test_future/test_urllib2.py
tests/test_future/test_urllib_response.py
tests/test_future/test_urllib_toplevel.py
tests/test_future/test_urllibnet.py
tests/test_future/test_urlparse.py
tests/test_future/test_utils.py
tests/test_past/__init__.py
tests/test_past/test_basestring.py
tests/test_past/test_builtins.py
tests/test_past/test_noniterators.py
tests/test_past/test_olddict.py
tests/test_past/test_oldstr.py
tests/test_past/test_translation.py