..\..\..\Scripts\futurize-script.py
..\..\..\Scripts\futurize.exe
..\..\..\Scripts\pasteurize-script.py
..\..\..\Scripts\pasteurize.exe
..\future\__init__.py
..\future\__pycache__\__init__.cpython-37.pyc
..\future\backports\__init__.py
..\future\backports\__pycache__\__init__.cpython-37.pyc
..\future\backports\__pycache__\_markupbase.cpython-37.pyc
..\future\backports\__pycache__\datetime.cpython-37.pyc
..\future\backports\__pycache__\misc.cpython-37.pyc
..\future\backports\__pycache__\socket.cpython-37.pyc
..\future\backports\__pycache__\socketserver.cpython-37.pyc
..\future\backports\__pycache__\total_ordering.cpython-37.pyc
..\future\backports\_markupbase.py
..\future\backports\datetime.py
..\future\backports\email\__init__.py
..\future\backports\email\__pycache__\__init__.cpython-37.pyc
..\future\backports\email\__pycache__\_encoded_words.cpython-37.pyc
..\future\backports\email\__pycache__\_header_value_parser.cpython-37.pyc
..\future\backports\email\__pycache__\_parseaddr.cpython-37.pyc
..\future\backports\email\__pycache__\_policybase.cpython-37.pyc
..\future\backports\email\__pycache__\base64mime.cpython-37.pyc
..\future\backports\email\__pycache__\charset.cpython-37.pyc
..\future\backports\email\__pycache__\encoders.cpython-37.pyc
..\future\backports\email\__pycache__\errors.cpython-37.pyc
..\future\backports\email\__pycache__\feedparser.cpython-37.pyc
..\future\backports\email\__pycache__\generator.cpython-37.pyc
..\future\backports\email\__pycache__\header.cpython-37.pyc
..\future\backports\email\__pycache__\headerregistry.cpython-37.pyc
..\future\backports\email\__pycache__\iterators.cpython-37.pyc
..\future\backports\email\__pycache__\message.cpython-37.pyc
..\future\backports\email\__pycache__\parser.cpython-37.pyc
..\future\backports\email\__pycache__\policy.cpython-37.pyc
..\future\backports\email\__pycache__\quoprimime.cpython-37.pyc
..\future\backports\email\__pycache__\utils.cpython-37.pyc
..\future\backports\email\_encoded_words.py
..\future\backports\email\_header_value_parser.py
..\future\backports\email\_parseaddr.py
..\future\backports\email\_policybase.py
..\future\backports\email\base64mime.py
..\future\backports\email\charset.py
..\future\backports\email\encoders.py
..\future\backports\email\errors.py
..\future\backports\email\feedparser.py
..\future\backports\email\generator.py
..\future\backports\email\header.py
..\future\backports\email\headerregistry.py
..\future\backports\email\iterators.py
..\future\backports\email\message.py
..\future\backports\email\mime\__init__.py
..\future\backports\email\mime\__pycache__\__init__.cpython-37.pyc
..\future\backports\email\mime\__pycache__\application.cpython-37.pyc
..\future\backports\email\mime\__pycache__\audio.cpython-37.pyc
..\future\backports\email\mime\__pycache__\base.cpython-37.pyc
..\future\backports\email\mime\__pycache__\image.cpython-37.pyc
..\future\backports\email\mime\__pycache__\message.cpython-37.pyc
..\future\backports\email\mime\__pycache__\multipart.cpython-37.pyc
..\future\backports\email\mime\__pycache__\nonmultipart.cpython-37.pyc
..\future\backports\email\mime\__pycache__\text.cpython-37.pyc
..\future\backports\email\mime\application.py
..\future\backports\email\mime\audio.py
..\future\backports\email\mime\base.py
..\future\backports\email\mime\image.py
..\future\backports\email\mime\message.py
..\future\backports\email\mime\multipart.py
..\future\backports\email\mime\nonmultipart.py
..\future\backports\email\mime\text.py
..\future\backports\email\parser.py
..\future\backports\email\policy.py
..\future\backports\email\quoprimime.py
..\future\backports\email\utils.py
..\future\backports\html\__init__.py
..\future\backports\html\__pycache__\__init__.cpython-37.pyc
..\future\backports\html\__pycache__\entities.cpython-37.pyc
..\future\backports\html\__pycache__\parser.cpython-37.pyc
..\future\backports\html\entities.py
..\future\backports\html\parser.py
..\future\backports\http\__init__.py
..\future\backports\http\__pycache__\__init__.cpython-37.pyc
..\future\backports\http\__pycache__\client.cpython-37.pyc
..\future\backports\http\__pycache__\cookiejar.cpython-37.pyc
..\future\backports\http\__pycache__\cookies.cpython-37.pyc
..\future\backports\http\__pycache__\server.cpython-37.pyc
..\future\backports\http\client.py
..\future\backports\http\cookiejar.py
..\future\backports\http\cookies.py
..\future\backports\http\server.py
..\future\backports\misc.py
..\future\backports\socket.py
..\future\backports\socketserver.py
..\future\backports\test\__init__.py
..\future\backports\test\__pycache__\__init__.cpython-37.pyc
..\future\backports\test\__pycache__\pystone.cpython-37.pyc
..\future\backports\test\__pycache__\ssl_servers.cpython-37.pyc
..\future\backports\test\__pycache__\support.cpython-37.pyc
..\future\backports\test\badcert.pem
..\future\backports\test\badkey.pem
..\future\backports\test\dh512.pem
..\future\backports\test\https_svn_python_org_root.pem
..\future\backports\test\keycert.passwd.pem
..\future\backports\test\keycert.pem
..\future\backports\test\keycert2.pem
..\future\backports\test\nokia.pem
..\future\backports\test\nullbytecert.pem
..\future\backports\test\nullcert.pem
..\future\backports\test\pystone.py
..\future\backports\test\sha256.pem
..\future\backports\test\ssl_cert.pem
..\future\backports\test\ssl_key.passwd.pem
..\future\backports\test\ssl_key.pem
..\future\backports\test\ssl_servers.py
..\future\backports\test\support.py
..\future\backports\total_ordering.py
..\future\backports\urllib\__init__.py
..\future\backports\urllib\__pycache__\__init__.cpython-37.pyc
..\future\backports\urllib\__pycache__\error.cpython-37.pyc
..\future\backports\urllib\__pycache__\parse.cpython-37.pyc
..\future\backports\urllib\__pycache__\request.cpython-37.pyc
..\future\backports\urllib\__pycache__\response.cpython-37.pyc
..\future\backports\urllib\__pycache__\robotparser.cpython-37.pyc
..\future\backports\urllib\error.py
..\future\backports\urllib\parse.py
..\future\backports\urllib\request.py
..\future\backports\urllib\response.py
..\future\backports\urllib\robotparser.py
..\future\backports\xmlrpc\__init__.py
..\future\backports\xmlrpc\__pycache__\__init__.cpython-37.pyc
..\future\backports\xmlrpc\__pycache__\client.cpython-37.pyc
..\future\backports\xmlrpc\__pycache__\server.cpython-37.pyc
..\future\backports\xmlrpc\client.py
..\future\backports\xmlrpc\server.py
..\future\builtins\__init__.py
..\future\builtins\__pycache__\__init__.cpython-37.pyc
..\future\builtins\__pycache__\disabled.cpython-37.pyc
..\future\builtins\__pycache__\iterators.cpython-37.pyc
..\future\builtins\__pycache__\misc.cpython-37.pyc
..\future\builtins\__pycache__\new_min_max.cpython-37.pyc
..\future\builtins\__pycache__\newnext.cpython-37.pyc
..\future\builtins\__pycache__\newround.cpython-37.pyc
..\future\builtins\__pycache__\newsuper.cpython-37.pyc
..\future\builtins\disabled.py
..\future\builtins\iterators.py
..\future\builtins\misc.py
..\future\builtins\new_min_max.py
..\future\builtins\newnext.py
..\future\builtins\newround.py
..\future\builtins\newsuper.py
..\future\moves\__init__.py
..\future\moves\__pycache__\__init__.cpython-37.pyc
..\future\moves\__pycache__\_dummy_thread.cpython-37.pyc
..\future\moves\__pycache__\_markupbase.cpython-37.pyc
..\future\moves\__pycache__\_thread.cpython-37.pyc
..\future\moves\__pycache__\builtins.cpython-37.pyc
..\future\moves\__pycache__\collections.cpython-37.pyc
..\future\moves\__pycache__\configparser.cpython-37.pyc
..\future\moves\__pycache__\copyreg.cpython-37.pyc
..\future\moves\__pycache__\itertools.cpython-37.pyc
..\future\moves\__pycache__\pickle.cpython-37.pyc
..\future\moves\__pycache__\queue.cpython-37.pyc
..\future\moves\__pycache__\reprlib.cpython-37.pyc
..\future\moves\__pycache__\socketserver.cpython-37.pyc
..\future\moves\__pycache__\subprocess.cpython-37.pyc
..\future\moves\__pycache__\sys.cpython-37.pyc
..\future\moves\__pycache__\winreg.cpython-37.pyc
..\future\moves\_dummy_thread.py
..\future\moves\_markupbase.py
..\future\moves\_thread.py
..\future\moves\builtins.py
..\future\moves\collections.py
..\future\moves\configparser.py
..\future\moves\copyreg.py
..\future\moves\dbm\__init__.py
..\future\moves\dbm\__pycache__\__init__.cpython-37.pyc
..\future\moves\dbm\__pycache__\dumb.cpython-37.pyc
..\future\moves\dbm\__pycache__\gnu.cpython-37.pyc
..\future\moves\dbm\__pycache__\ndbm.cpython-37.pyc
..\future\moves\dbm\dumb.py
..\future\moves\dbm\gnu.py
..\future\moves\dbm\ndbm.py
..\future\moves\html\__init__.py
..\future\moves\html\__pycache__\__init__.cpython-37.pyc
..\future\moves\html\__pycache__\entities.cpython-37.pyc
..\future\moves\html\__pycache__\parser.cpython-37.pyc
..\future\moves\html\entities.py
..\future\moves\html\parser.py
..\future\moves\http\__init__.py
..\future\moves\http\__pycache__\__init__.cpython-37.pyc
..\future\moves\http\__pycache__\client.cpython-37.pyc
..\future\moves\http\__pycache__\cookiejar.cpython-37.pyc
..\future\moves\http\__pycache__\cookies.cpython-37.pyc
..\future\moves\http\__pycache__\server.cpython-37.pyc
..\future\moves\http\client.py
..\future\moves\http\cookiejar.py
..\future\moves\http\cookies.py
..\future\moves\http\server.py
..\future\moves\itertools.py
..\future\moves\pickle.py
..\future\moves\queue.py
..\future\moves\reprlib.py
..\future\moves\socketserver.py
..\future\moves\subprocess.py
..\future\moves\sys.py
..\future\moves\test\__init__.py
..\future\moves\test\__pycache__\__init__.cpython-37.pyc
..\future\moves\test\__pycache__\support.cpython-37.pyc
..\future\moves\test\support.py
..\future\moves\tkinter\__init__.py
..\future\moves\tkinter\__pycache__\__init__.cpython-37.pyc
..\future\moves\tkinter\__pycache__\colorchooser.cpython-37.pyc
..\future\moves\tkinter\__pycache__\commondialog.cpython-37.pyc
..\future\moves\tkinter\__pycache__\constants.cpython-37.pyc
..\future\moves\tkinter\__pycache__\dialog.cpython-37.pyc
..\future\moves\tkinter\__pycache__\dnd.cpython-37.pyc
..\future\moves\tkinter\__pycache__\filedialog.cpython-37.pyc
..\future\moves\tkinter\__pycache__\font.cpython-37.pyc
..\future\moves\tkinter\__pycache__\messagebox.cpython-37.pyc
..\future\moves\tkinter\__pycache__\scrolledtext.cpython-37.pyc
..\future\moves\tkinter\__pycache__\simpledialog.cpython-37.pyc
..\future\moves\tkinter\__pycache__\tix.cpython-37.pyc
..\future\moves\tkinter\__pycache__\ttk.cpython-37.pyc
..\future\moves\tkinter\colorchooser.py
..\future\moves\tkinter\commondialog.py
..\future\moves\tkinter\constants.py
..\future\moves\tkinter\dialog.py
..\future\moves\tkinter\dnd.py
..\future\moves\tkinter\filedialog.py
..\future\moves\tkinter\font.py
..\future\moves\tkinter\messagebox.py
..\future\moves\tkinter\scrolledtext.py
..\future\moves\tkinter\simpledialog.py
..\future\moves\tkinter\tix.py
..\future\moves\tkinter\ttk.py
..\future\moves\urllib\__init__.py
..\future\moves\urllib\__pycache__\__init__.cpython-37.pyc
..\future\moves\urllib\__pycache__\error.cpython-37.pyc
..\future\moves\urllib\__pycache__\parse.cpython-37.pyc
..\future\moves\urllib\__pycache__\request.cpython-37.pyc
..\future\moves\urllib\__pycache__\response.cpython-37.pyc
..\future\moves\urllib\__pycache__\robotparser.cpython-37.pyc
..\future\moves\urllib\error.py
..\future\moves\urllib\parse.py
..\future\moves\urllib\request.py
..\future\moves\urllib\response.py
..\future\moves\urllib\robotparser.py
..\future\moves\winreg.py
..\future\moves\xmlrpc\__init__.py
..\future\moves\xmlrpc\__pycache__\__init__.cpython-37.pyc
..\future\moves\xmlrpc\__pycache__\client.cpython-37.pyc
..\future\moves\xmlrpc\__pycache__\server.cpython-37.pyc
..\future\moves\xmlrpc\client.py
..\future\moves\xmlrpc\server.py
..\future\standard_library\__init__.py
..\future\standard_library\__pycache__\__init__.cpython-37.pyc
..\future\tests\__init__.py
..\future\tests\__pycache__\__init__.cpython-37.pyc
..\future\tests\__pycache__\base.cpython-37.pyc
..\future\tests\base.py
..\future\types\__init__.py
..\future\types\__pycache__\__init__.cpython-37.pyc
..\future\types\__pycache__\newbytes.cpython-37.pyc
..\future\types\__pycache__\newdict.cpython-37.pyc
..\future\types\__pycache__\newint.cpython-37.pyc
..\future\types\__pycache__\newlist.cpython-37.pyc
..\future\types\__pycache__\newmemoryview.cpython-37.pyc
..\future\types\__pycache__\newobject.cpython-37.pyc
..\future\types\__pycache__\newopen.cpython-37.pyc
..\future\types\__pycache__\newrange.cpython-37.pyc
..\future\types\__pycache__\newstr.cpython-37.pyc
..\future\types\newbytes.py
..\future\types\newdict.py
..\future\types\newint.py
..\future\types\newlist.py
..\future\types\newmemoryview.py
..\future\types\newobject.py
..\future\types\newopen.py
..\future\types\newrange.py
..\future\types\newstr.py
..\future\utils\__init__.py
..\future\utils\__pycache__\__init__.cpython-37.pyc
..\future\utils\__pycache__\surrogateescape.cpython-37.pyc
..\future\utils\surrogateescape.py
..\libfuturize\__init__.py
..\libfuturize\__pycache__\__init__.cpython-37.pyc
..\libfuturize\__pycache__\fixer_util.cpython-37.pyc
..\libfuturize\__pycache__\main.cpython-37.pyc
..\libfuturize\fixer_util.py
..\libfuturize\fixes\__init__.py
..\libfuturize\fixes\__pycache__\__init__.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_UserDict.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_absolute_import.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_add__future__imports_except_unicode_literals.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_basestring.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_bytes.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_cmp.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_division.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_division_safe.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_execfile.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_future_builtins.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_future_standard_library.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_future_standard_library_urllib.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_input.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_metaclass.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_next_call.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_object.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_oldstr_wrap.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_order___future__imports.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_print.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_print_with_import.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_raise.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_remove_old__future__imports.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_unicode_keep_u.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_unicode_literals_import.cpython-37.pyc
..\libfuturize\fixes\__pycache__\fix_xrange_with_import.cpython-37.pyc
..\libfuturize\fixes\fix_UserDict.py
..\libfuturize\fixes\fix_absolute_import.py
..\libfuturize\fixes\fix_add__future__imports_except_unicode_literals.py
..\libfuturize\fixes\fix_basestring.py
..\libfuturize\fixes\fix_bytes.py
..\libfuturize\fixes\fix_cmp.py
..\libfuturize\fixes\fix_division.py
..\libfuturize\fixes\fix_division_safe.py
..\libfuturize\fixes\fix_execfile.py
..\libfuturize\fixes\fix_future_builtins.py
..\libfuturize\fixes\fix_future_standard_library.py
..\libfuturize\fixes\fix_future_standard_library_urllib.py
..\libfuturize\fixes\fix_input.py
..\libfuturize\fixes\fix_metaclass.py
..\libfuturize\fixes\fix_next_call.py
..\libfuturize\fixes\fix_object.py
..\libfuturize\fixes\fix_oldstr_wrap.py
..\libfuturize\fixes\fix_order___future__imports.py
..\libfuturize\fixes\fix_print.py
..\libfuturize\fixes\fix_print_with_import.py
..\libfuturize\fixes\fix_raise.py
..\libfuturize\fixes\fix_remove_old__future__imports.py
..\libfuturize\fixes\fix_unicode_keep_u.py
..\libfuturize\fixes\fix_unicode_literals_import.py
..\libfuturize\fixes\fix_xrange_with_import.py
..\libfuturize\main.py
..\libpasteurize\__init__.py
..\libpasteurize\__pycache__\__init__.cpython-37.pyc
..\libpasteurize\__pycache__\main.cpython-37.pyc
..\libpasteurize\fixes\__init__.py
..\libpasteurize\fixes\__pycache__\__init__.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\feature_base.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_add_all__future__imports.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_add_all_future_builtins.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_add_future_standard_library_import.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_annotations.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_division.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_features.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_fullargspec.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_future_builtins.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_getcwd.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_imports.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_imports2.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_kwargs.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_memoryview.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_metaclass.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_newstyle.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_next.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_printfunction.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_raise.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_raise_.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_throw.cpython-37.pyc
..\libpasteurize\fixes\__pycache__\fix_unpacking.cpython-37.pyc
..\libpasteurize\fixes\feature_base.py
..\libpasteurize\fixes\fix_add_all__future__imports.py
..\libpasteurize\fixes\fix_add_all_future_builtins.py
..\libpasteurize\fixes\fix_add_future_standard_library_import.py
..\libpasteurize\fixes\fix_annotations.py
..\libpasteurize\fixes\fix_division.py
..\libpasteurize\fixes\fix_features.py
..\libpasteurize\fixes\fix_fullargspec.py
..\libpasteurize\fixes\fix_future_builtins.py
..\libpasteurize\fixes\fix_getcwd.py
..\libpasteurize\fixes\fix_imports.py
..\libpasteurize\fixes\fix_imports2.py
..\libpasteurize\fixes\fix_kwargs.py
..\libpasteurize\fixes\fix_memoryview.py
..\libpasteurize\fixes\fix_metaclass.py
..\libpasteurize\fixes\fix_newstyle.py
..\libpasteurize\fixes\fix_next.py
..\libpasteurize\fixes\fix_printfunction.py
..\libpasteurize\fixes\fix_raise.py
..\libpasteurize\fixes\fix_raise_.py
..\libpasteurize\fixes\fix_throw.py
..\libpasteurize\fixes\fix_unpacking.py
..\libpasteurize\main.py
..\past\__init__.py
..\past\__pycache__\__init__.cpython-37.pyc
..\past\builtins\__init__.py
..\past\builtins\__pycache__\__init__.cpython-37.pyc
..\past\builtins\__pycache__\misc.cpython-37.pyc
..\past\builtins\__pycache__\noniterators.cpython-37.pyc
..\past\builtins\misc.py
..\past\builtins\noniterators.py
..\past\translation\__init__.py
..\past\translation\__pycache__\__init__.cpython-37.pyc
..\past\types\__init__.py
..\past\types\__pycache__\__init__.cpython-37.pyc
..\past\types\__pycache__\basestring.cpython-37.pyc
..\past\types\__pycache__\olddict.cpython-37.pyc
..\past\types\__pycache__\oldstr.cpython-37.pyc
..\past\types\basestring.py
..\past\types\olddict.py
..\past\types\oldstr.py
..\past\utils\__init__.py
..\past\utils\__pycache__\__init__.cpython-37.pyc
PKG-INFO
SOURCES.txt
dependency_links.txt
entry_points.txt
top_level.txt
